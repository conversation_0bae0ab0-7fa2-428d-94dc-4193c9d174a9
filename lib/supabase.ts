import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';
import { useAuth } from '@clerk/clerk-expo';

const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl || process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey || process.env.EXPO_PUBLIC_SUPABASE_KEY;

// Supabase configuration loaded successfully

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.');
}

// Validate URL format
try {
  new URL(supabaseUrl);
} catch (error) {
  throw new Error('Invalid Supabase URL format. Please check your configuration.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true, // Enable for OAuth redirects on web
    flowType: 'pkce', // Use PKCE flow for better security
  },
});

// Create authenticated Supabase client using Clerk token
export const createAuthenticatedClient = async (getToken: (options?: { template?: string }) => Promise<string | null>) => {
  const token = await getToken({ template: 'supabase' });
  
  if (!token) {
    throw new Error('No authentication token available');
  }

  return createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false,
    },
  });
};

// Create authenticated client with automatic token refresh
export const useSupabaseClient = () => {
  const { getToken } = useAuth();
  
  return {
    getAuthenticatedClient: async () => {
      return createAuthenticatedClient(getToken);
    },
    getToken: () => getToken({ template: 'supabase' })
  };
};

// Test connection on initialization
supabase.auth.getSession().then(({ error }) => {
  if (error) {
    // Supabase connection test failed - handle silently in production
  }
}).catch((error) => {
  // Supabase initialization error - handle silently in production
});
