import { supabase, createAuthenticatedClient, useSupabaseClient } from '@/lib/supabase';
import { Plant, GardenPlant } from '@/types/plant';

// Helper function to map diagnosis severity to health status
const mapSeverityToHealthStatus = (severity?: string): 'healthy' | 'sick' | 'recovering' | 'critical' => {
  if (!severity) return 'healthy';

  const normalizedSeverity = severity.toLowerCase().trim();

  switch (normalizedSeverity) {
    case 'mild':
    case 'moderate':
    case 'medium':
      return 'sick';
    case 'severe':
    case 'critical':
    case 'high':
      return 'critical';
    case 'healthy':
    case 'none':
    case 'no issues':
    case 'no problems':
    case 'good':
    case 'fine':
    case 'normal':
    case 'low':
    case 'not applicable':
    case 'n/a':
      return 'healthy';
    default:
      // If we can't determine the severity, default to healthy for diagnosed plants
      // since the user specifically asked for a diagnosis
      return 'healthy';
  }
};

export interface UserProfile {
  id: string;
  user_id: string;
  username?: string;
  display_name?: string;
  bio?: string;
  avatar_url?: string;
  location?: string;
  website_url?: string;
  is_public: boolean;
  allow_garden_sharing: boolean;
  allow_profile_indexing: boolean;
  experience_level: 'beginner' | 'intermediate' | 'expert';
  total_identifications: number;
  total_diagnoses: number;
  created_at: string;
  updated_at: string;
}

export interface PlantIdentification {
  id: string;
  user_id: string;
  image_url: string;
  scientific_name?: string;
  common_name?: string;
  family_name?: string;
  description?: string;
  care_instructions?: string;
  tags?: string[];
  confidence_score?: number;
  identification_source: string;
  is_verified: boolean;
  is_public: boolean;
  location_taken?: string;
  created_at: string;
  updated_at: string;
  slug?: string;
  // New fields for enhanced plant information
  plant_type?: string;
  native_region?: string;
  toxicity_level?: string;
  toxicity_warning?: string;
  growth_habit?: string;
  growth_rate?: string;
  mature_height?: string;
  mature_width?: string;
  mature_description?: string;
  bloom_time?: string;
  flower_colors?: string[];
  foliage_type?: string;
  hardiness_zones?: string;
  min_temperature?: string;
  pests_and_diseases?: string;
  fun_facts?: string[];
  uses?: string[];
  propagation?: string;
  seasonal_care?: string;
  companion_plants?: string[];
  maintenance_level?: string;
  // Garden-related fields (moved from garden_collections)
  notes?: string;
  nickname?: string;
  health_status?: 'healthy' | 'sick' | 'recovering' | 'critical';
  location_in_garden?: string;
  date_acquired?: string;
  last_watered?: string;
  last_fertilized?: string;
  last_repotted?: string;
  watering_frequency_days?: number;
  fertilizing_frequency_days?: number;
  allow_community_tips?: boolean;
}

export interface GardenCollection {
  id: string;
  user_id: string;
  plant_identification_id?: string;
  nickname?: string;
  notes?: string;
  health_status: 'healthy' | 'sick' | 'recovering' | 'critical';
  location_in_garden?: string;
  date_acquired?: string;
  last_watered?: string;
  last_fertilized?: string;
  last_repotted?: string;
  watering_frequency_days?: number;
  fertilizing_frequency_days?: number;
  is_public: boolean;
  allow_community_tips: boolean;
  created_at: string;
  updated_at: string;
  plant_identifications?: PlantIdentification;
  plant_diagnoses?: PlantDiagnosis[];
}

export interface PlantDiagnosis {
  id: string;
  user_id: string;
  garden_collection_id?: string; // Will be deprecated
  plant_identification_id?: string;
  image_url: string;
  problem_description?: string;
  diagnosed_problem?: string;
  likely_causes?: string[];
  symptoms_observed?: string;
  severity?: 'mild' | 'moderate' | 'severe' | 'critical';
  immediate_actions?: string[];
  long_term_care?: string[];
  product_recommendations?: string[];
  step_by_step_instructions?: string[];
  prevention_tips?: string[];
  prognosis?: string;
  confidence_score?: number;
  diagnosis_source: string;
  is_verified: boolean;
  verified_by?: string;
  verified_at?: string;
  is_public: boolean;
  notes?: string; // User's personal notes about the diagnosis
  location?: string; // Location where the plant photo was taken
  nickname?: string; // User-given nickname for the plant
  health_status?: 'healthy' | 'sick' | 'recovering' | 'critical';
  location_in_garden?: string; // Location of the plant within user garden
  date_acquired?: string;
  last_watered?: string;
  last_fertilized?: string;
  last_repotted?: string;
  watering_frequency_days?: number;
  fertilizing_frequency_days?: number;
  allow_community_tips?: boolean;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  slug?: string;
  care_instructions?: any; // JSONB field
  created_at: string;
  updated_at: string;

  // Plant identification fields (now stored directly in plant_diagnoses)
  scientific_name?: string;
  common_name?: string;
  description?: string;
  plant_type?: string;
  native_region?: string;
  toxicity_level?: string;
  toxicity_warning?: string;
  growth_habit?: string;
  growth_rate?: string;
  mature_height?: string;
  mature_width?: string;
  mature_description?: string;
  bloom_time?: string;
  flower_colors?: string[];
  foliage_type?: string;
  hardiness_zones?: string;
  min_temperature?: string;
  pests_and_diseases?: string;
  fun_facts?: string[];
  uses?: string[];
  propagation?: string;
  seasonal_care?: string;
  companion_plants?: string[];
  maintenance_level?: string;
  tags?: string[];

  // Joined relation (via plant_identification_id) - will be deprecated
  plant_identifications?: PlantIdentification;
}

export interface RecoveryTracking {
  id: string;
  diagnosis_id: string;
  user_id: string;
  progress_image_url?: string;
  recovery_status: 'in_progress' | 'improving' | 'recovered' | 'worsening' | 'failed';
  progress_notes?: string;
  treatments_applied?: string[];
  date_applied: string;
  effectiveness_rating?: number;
  side_effects?: string;
  next_steps?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export class DatabaseService {
  // Helper to get authenticated client with getToken function
  private static async getAuthenticatedClient(getToken: (options?: { template?: string }) => Promise<string | null>) {
    try {
      return await createAuthenticatedClient(getToken);
    } catch (error) {
      console.error('Failed to create authenticated client:', error);
      throw new Error('Authentication failed. Please log out and back in.');
    }
  }

  // User Profile methods
  static async getUserProfile(userId: string, getToken?: (options?: { template?: string }) => Promise<string | null>): Promise<UserProfile | null> {
    try {
      let client = supabase;
      
      // Use authenticated client if getToken is provided (Clerk auth)
      if (getToken) {
        try {
          client = await this.getAuthenticatedClient(getToken);
        } catch (authError) {
          console.warn('Failed to get authenticated client, falling back to standard client:', authError);
          // Fall back to standard client for backwards compatibility
        }
      }

      const { data, error } = await client
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          // No profile found - this is expected for new users
          return null;
        } else if (error.message.includes('JWT') || error.message.includes('401')) {
          throw new Error('Session expired. Please log out and back in.');
        } else if (error.message.includes('network')) {
          throw new Error('Network error. Please check your connection and try again.');
        } else if (error.code === 'PGRST301' || error.message.includes('401')) {
          throw new Error('Authentication failed. Please log out and back in.');
        } else {
          throw new Error(`Failed to load profile: ${error.message}`);
        }
      }

      return data;
    } catch (error) {
      // Re-throw if it's already a custom error
      if (error instanceof Error) {
        throw error;
      }

      // Generic error for unexpected issues
      throw new Error('Failed to load profile. Please try again.');
    }
  }

  static async createUserProfile(profile: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>): Promise<UserProfile | null> {
    const { data, error } = await supabase
      .from('user_profiles')
      .insert(profile)
      .select()
      .single();

    if (error) {
      console.error('Error creating user profile:', error);
      return null;
    }

    return data;
  }

  static async updateUserProfile(userId: string, updates: Partial<UserProfile>, getToken?: (options?: { template?: string }) => Promise<string | null>): Promise<UserProfile | null> {
    try {
      console.log('DatabaseService.updateUserProfile called with:', { userId, updates });

      let client = supabase;
      
      // Use authenticated client if getToken is provided (Clerk auth)
      if (getToken) {
        try {
          client = await this.getAuthenticatedClient(getToken);
          console.log('Using authenticated Clerk client for profile update');
        } catch (authError) {
          console.error('Failed to get authenticated client:', authError);
          throw new Error('Authentication failed. Please log out and back in.');
        }
      } else {
        // Check current session before making the call for backwards compatibility
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        console.log('Current session in updateUserProfile:', {
          hasSession: !!session,
          sessionError: sessionError?.message,
          expiresAt: session?.expires_at
        });
      }

      const { data, error } = await client
        .from('user_profiles')
        .update(updates)
        .eq('user_id', userId)
        .select()
        .single();

      console.log('Update result:', { data: !!data, error: error?.message });

      if (error) {
        console.error('Error updating user profile:', error);

        // Provide more specific error messages
        if (error.code === 'PGRST116') {
          throw new Error('Profile not found. Please try logging out and back in.');
        } else if (error.code === '23505') {
          throw new Error('Username already taken. Please choose a different username.');
        } else if (error.message.includes('JWT') || error.message.includes('401')) {
          throw new Error('Session expired. Please log out and back in.');
        } else if (error.message.includes('network')) {
          throw new Error('Network error. Please check your connection and try again.');
        } else if (error.code === 'PGRST301' || error.message.includes('401')) {
          throw new Error('Authentication failed. Please log out and back in.');
        } else {
          throw new Error(`Database error: ${error.message}`);
        }
      }

      return data;
    } catch (error) {
      console.error('Error in updateUserProfile:', error);

      // Re-throw if it's already a custom error
      if (error instanceof Error) {
        throw error;
      }

      // Generic error for unexpected issues
      throw new Error('Failed to update profile. Please try again.');
    }
  }

  static async updateUserProfileStats(
    userId: string,
    getToken?: (options?: { template?: string }) => Promise<string | null>
  ): Promise<UserProfile | null> {
    try {
      let client = supabase;
      if (getToken) {
        try {
          client = await this.getAuthenticatedClient(getToken);
        } catch (authError) {
          console.warn('Failed to get authenticated client, falling back to standard client:', authError);
        }
      }

      // Get current counts from database
      const [identificationsResult, diagnosesResult] = await Promise.all([
        client
          .from('plant_identifications')
          .select('id', { count: 'exact' })
          .eq('user_id', userId),
        client
          .from('plant_diagnoses')
          .select('id', { count: 'exact' })
          .eq('user_id', userId)
      ]);

      const identificationCount = identificationsResult.count || 0;
      const diagnosisCount = diagnosesResult.count || 0;

      // Update user profile with current counts
      return await this.updateUserProfile(userId, {
        total_identifications: identificationCount,
        total_diagnoses: diagnosisCount,
      }, getToken);
    } catch (error) {
      console.error('Error updating user profile stats:', error);
      return null;
    }
  }

  // Plant Identification methods
  static async createPlantIdentification(identification: Omit<PlantIdentification, 'id' | 'created_at' | 'updated_at'>, getToken?: (options?: { template?: string }) => Promise<string | null>): Promise<PlantIdentification> {
    try {
      let client = supabase;
      
      // Use authenticated client if getToken is provided (Clerk auth)
      if (getToken) {
        try {
          client = await this.getAuthenticatedClient(getToken);
          console.log('Using authenticated Clerk client for plant identification');
        } catch (authError) {
          console.error('Failed to get authenticated client:', authError);
          throw new Error('Authentication failed. Please log out and back in.');
        }
      }

      const { data, error } = await client
        .from('plant_identifications')
        .insert(identification)
        .select()
        .single();

      if (error) {
        console.error('Error creating plant identification:', error);
        // Provide more specific error messages
        if (error.code === '23505') {
          throw new Error('This plant has already been identified and added to your garden.');
        } else if (error.code === '23502') {
          throw new Error('Missing required information for plant identification.');
        } else if (error.code === '23514') {
          throw new Error('Invalid data provided for plant identification.');
        } else if (error.message.includes('JWT') || error.message.includes('401')) {
          throw new Error('Session expired. Please log out and back in.');
        } else if (error.message.includes('network')) {
          throw new Error('Network error. Please check your connection and try again.');
        } else if (error.message.includes('row-level security policy')) {
          throw new Error('Authentication failed. Please log out and back in.');
        } else {
          throw new Error(`Failed to save plant identification: ${error.message}`);
        }
      }

      return data;
    } catch (error) {
      console.error('Error in createPlantIdentification:', error);

      // Re-throw if it's already a custom error
      if (error instanceof Error) {
        throw error;
      }

      // Generic error for unexpected issues
      throw new Error('Failed to save plant identification. Please try again.');
    }
  }

  static async getPlantIdentification(id: string): Promise<PlantIdentification | null> {
    const { data, error } = await supabase
      .from('plant_identifications')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching plant identification:', error);
      return null;
    }

    return data;
  }

  static async getPlantIdentifications(userId: string, getToken?: (options?: { template?: string }) => Promise<string | null>): Promise<PlantIdentification[]> {
    try {
      let client = supabase;
      
      // Use authenticated client if getToken is provided (Clerk auth)
      if (getToken) {
        try {
          client = await this.getAuthenticatedClient(getToken);
        } catch (authError) {
          console.warn('Failed to get authenticated client, falling back to standard client:', authError);
        }
      }

      const { data, error } = await client
        .from('plant_identifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching plant identifications:', error);
        if (error.message.includes('JWT') || error.message.includes('401')) {
          throw new Error('Session expired. Please log out and back in.');
        } else if (error.message.includes('row-level security policy')) {
          throw new Error('Authentication failed. Please log out and back in.');
        }
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getPlantIdentifications:', error);
      if (error instanceof Error) {
        throw error;
      }
      return [];
    }
  }

  static async getRecentPlantIdentifications(userId: string, limit: number = 5): Promise<PlantIdentification[]> {
    const { data, error } = await supabase
      .from('plant_identifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent plant identifications:', error);
      return [];
    }

    return data || [];
  }

  static async updatePlantIdentification(
    id: string,
    updates: Partial<PlantIdentification>,
    getToken?: (options?: { template?: string }) => Promise<string | null>
  ): Promise<PlantIdentification | null> {
    if (!id) {
      throw new Error('Plant identification ID is required for update');
    }

    let client = supabase;
    // Use authenticated client if getToken is provided (Clerk auth)
    if (getToken) {
      try {
        client = await this.getAuthenticatedClient(getToken);
      } catch (authError) {
        console.error('Failed to get authenticated client:', authError);
        throw new Error('Authentication failed. Please log out and back in.');
      }
    }

    // First, fetch the record to get user_id and all fields for return type
    const { data: existingRecord, error: fetchError } = await client
      .from('plant_identifications')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !existingRecord) {
      console.error('Plant identification not found:', fetchError);
      throw new Error(`Plant identification with ID ${id} not found`);
    }

    // Only update fields that exist in the plant_identifications table
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    // Map common garden fields (these come from garden.tsx in snake_case format)
    if (updates.nickname !== undefined) updateData.nickname = updates.nickname;
    if (updates.notes !== undefined) updateData.notes = updates.notes;
    if (updates.location_in_garden !== undefined) updateData.location_in_garden = updates.location_in_garden;
    if (updates.health_status !== undefined) updateData.health_status = updates.health_status;
    if (updates.is_public !== undefined) updateData.is_public = updates.is_public;
    if (updates.allow_community_tips !== undefined) updateData.allow_community_tips = updates.allow_community_tips;
    if (updates.last_watered !== undefined) updateData.last_watered = updates.last_watered;
    if (updates.last_fertilized !== undefined) updateData.last_fertilized = updates.last_fertilized;

    // Map plant identification fields
    if (updates.common_name !== undefined) updateData.common_name = updates.common_name;
    if (updates.scientific_name !== undefined) updateData.scientific_name = updates.scientific_name;
    if (updates.description !== undefined) updateData.description = updates.description;
    if (updates.care_instructions !== undefined) updateData.care_instructions = updates.care_instructions;
    if (updates.tags !== undefined) updateData.tags = updates.tags;

    // Ensure we have at least one field to update besides updated_at
    const fieldsToUpdate = Object.keys(updateData).filter(key => key !== 'updated_at');
    if (fieldsToUpdate.length === 0) {
      console.log('No fields to update for plant identification:', id);
      return existingRecord; // Return existing record if no changes
    }

    console.log('Updating plant identification with data:', updateData);

    // Filter update by both id and user_id for RLS
    const { data, error } = await client
      .from('plant_identifications')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', existingRecord.user_id)
      .select()
      .single();

    if (error) {
      console.error('Error updating plant identification:', error);
      // Provide more specific error messages
      if (error.code === 'PGRST116') {
        throw new Error('Plant identification not found or no changes made');
      } else if (error.message.includes('JWT') || error.message.includes('401')) {
        throw new Error('Session expired. Please log out and back in.');
      } else if (error.message.includes('network')) {
        throw new Error('Network error. Please check your connection and try again.');
      } else if (error.message.includes('row-level security policy')) {
        throw new Error('Authentication failed. Please log out and back in.');
      } else {
        throw new Error(`Failed to update plant identification: ${error.message}`);
      }
    }

    return data;
  }

  static async removePlantIdentification(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('plant_identifications')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error removing plant identification:', error);
      return false;
    }

    return true;
  }

  // Garden Collection methods - REMOVED: Tables dropped
  // Functions that previously wrote to garden_collections table have been removed
  // Garden functionality is now handled through plant_identifications and plant_diagnoses tables

  // Plant Diagnosis methods
  static async createPlantDiagnosis(diagnosis: Omit<PlantDiagnosis, 'id' | 'created_at' | 'updated_at'>, getToken?: (options?: { template?: string }) => Promise<string | null>): Promise<PlantDiagnosis> {
    try {
      let client = supabase;
      
      // Use authenticated client if getToken is provided (Clerk auth)
      if (getToken) {
        try {
          client = await this.getAuthenticatedClient(getToken);
          console.log('Using authenticated Clerk client for plant diagnosis');
        } catch (authError) {
          console.error('Failed to get authenticated client:', authError);
          throw new Error('Authentication failed. Please log out and back in.');
        }
      }

      const { data, error } = await client
        .from('plant_diagnoses')
        .insert(diagnosis)
        .select()
        .single();

      if (error) {
        console.error('Error creating plant diagnosis:', error);
        // Provide more specific error messages
        if (error.code === '23505') {
          throw new Error('A diagnosis for this plant already exists.');
        } else if (error.code === '23502') {
          throw new Error('Missing required information for plant diagnosis.');
        } else if (error.code === '23514') {
          throw new Error('Invalid data provided for plant diagnosis.');
        } else if (error.message.includes('JWT') || error.message.includes('401')) {
          throw new Error('Session expired. Please log out and back in.');
        } else if (error.message.includes('network')) {
          throw new Error('Network error. Please check your connection and try again.');
        } else if (error.message.includes('row-level security policy')) {
          throw new Error('Failed to save plant identification: new row violates row-level security policy for table "plant_diagnoses"');
        } else {
          throw new Error(`Failed to save diagnosis: ${error.message}`);
        }
      }

      return data;
    } catch (error) {
      console.error('Error in createPlantDiagnosis:', error);

      // Re-throw if it's already a custom error
      if (error instanceof Error) {
        throw error;
      }

      // Generic error for unexpected issues
      throw new Error('Failed to save plant diagnosis. Please try again.');
    }
  }

  static async getPlantDiagnosis(id: string): Promise<PlantDiagnosis | null> {
    const { data, error } = await supabase
      .from('plant_diagnoses')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching plant diagnosis:', error);
      return null;
    }

    return data;
  }

  static async getPlantDiagnoses(userId: string, getToken?: (options?: { template?: string }) => Promise<string | null>): Promise<PlantDiagnosis[]> {
    try {
      let client = supabase;
      
      // Use authenticated client if getToken is provided (Clerk auth)
      if (getToken) {
        try {
          client = await this.getAuthenticatedClient(getToken);
        } catch (authError) {
          console.warn('Failed to get authenticated client, falling back to standard client:', authError);
        }
      }

      const { data, error } = await client
        .from('plant_diagnoses')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching plant diagnoses:', error);
        if (error.message.includes('JWT') || error.message.includes('401')) {
          throw new Error('Session expired. Please log out and back in.');
        } else if (error.message.includes('row-level security policy')) {
          throw new Error('Authentication failed. Please log out and back in.');
        }
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getPlantDiagnoses:', error);
      if (error instanceof Error) {
        throw error;
      }
      return [];
    }
  }

  static async getRecentPlantDiagnoses(userId: string, limit: number = 5): Promise<PlantDiagnosis[]> {
    const { data, error } = await supabase
      .from('plant_diagnoses')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent plant diagnoses:', error);
      return [];
    }

    return data || [];
  }

  static async updatePlantDiagnosis(
    id: string,
    updates: Partial<PlantDiagnosis>,
    getToken?: (options?: { template?: string }) => Promise<string | null>
  ): Promise<PlantDiagnosis | null> {
    if (!id) {
      throw new Error('Plant diagnosis ID is required for update');
    }

    let client = supabase;
    // Use authenticated client if getToken is provided (Clerk auth)
    if (getToken) {
      try {
        client = await this.getAuthenticatedClient(getToken);
      } catch (authError) {
        console.error('Failed to get authenticated client:', authError);
        throw new Error('Authentication failed. Please log out and back in.');
      }
    }

    // First, fetch the record to get user_id and all fields for return type
    const { data: existingRecord, error: fetchError } = await client
      .from('plant_diagnoses')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !existingRecord) {
      console.error('Plant diagnosis not found:', fetchError);
      throw new Error(`Plant diagnosis with ID ${id} not found`);
    }

    // Only update fields that exist in the plant_diagnoses table
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    // Map common garden fields (these come from garden.tsx in snake_case format)
    if (updates.nickname !== undefined) updateData.nickname = updates.nickname;
    if (updates.notes !== undefined) updateData.notes = updates.notes;
    if (updates.location_in_garden !== undefined) updateData.location_in_garden = updates.location_in_garden;
    if (updates.health_status !== undefined) updateData.health_status = updates.health_status;
    if (updates.is_public !== undefined) updateData.is_public = updates.is_public;
    if (updates.allow_community_tips !== undefined) updateData.allow_community_tips = updates.allow_community_tips;
    if (updates.last_watered !== undefined) updateData.last_watered = updates.last_watered;
    if (updates.last_fertilized !== undefined) updateData.last_fertilized = updates.last_fertilized;

    // Map diagnosis-specific fields
    if (updates.diagnosed_problem !== undefined) updateData.diagnosed_problem = updates.diagnosed_problem;
    if (updates.likely_causes !== undefined) updateData.likely_causes = updates.likely_causes;
    if (updates.symptoms_observed !== undefined) updateData.symptoms_observed = updates.symptoms_observed;
    if (updates.severity !== undefined) updateData.severity = updates.severity;
    if (updates.immediate_actions !== undefined) updateData.immediate_actions = updates.immediate_actions;
    if (updates.long_term_care !== undefined) updateData.long_term_care = updates.long_term_care;
    if (updates.product_recommendations !== undefined) updateData.product_recommendations = updates.product_recommendations;
    if (updates.step_by_step_instructions !== undefined) updateData.step_by_step_instructions = updates.step_by_step_instructions;
    if (updates.prevention_tips !== undefined) updateData.prevention_tips = updates.prevention_tips;
    if (updates.prognosis !== undefined) updateData.prognosis = updates.prognosis;
    if (updates.confidence_score !== undefined) updateData.confidence_score = updates.confidence_score;

    // Map plant identification fields stored in diagnosis
    if (updates.common_name !== undefined) updateData.common_name = updates.common_name;
    if (updates.scientific_name !== undefined) updateData.scientific_name = updates.scientific_name;
    if (updates.description !== undefined) updateData.description = updates.description;
    if (updates.care_instructions !== undefined) updateData.care_instructions = updates.care_instructions;

    // Ensure we have at least one field to update besides updated_at
    const fieldsToUpdate = Object.keys(updateData).filter(key => key !== 'updated_at');
    if (fieldsToUpdate.length === 0) {
      console.log('No fields to update for plant diagnosis:', id);
      return existingRecord; // Return existing record if no changes
    }

    console.log('Updating plant diagnosis with data:', updateData);

    // Filter update by both id and user_id for RLS
    const { data, error } = await client
      .from('plant_diagnoses')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', existingRecord.user_id)
      .select()
      .single();

    if (error) {
      console.error('Error updating plant diagnosis:', error);
      // Provide more specific error messages
      if (error.code === 'PGRST116') {
        throw new Error('Plant diagnosis not found or no changes made');
      } else if (error.message.includes('JWT') || error.message.includes('401')) {
        throw new Error('Session expired. Please log out and back in.');
      } else if (error.message.includes('network')) {
        throw new Error('Network error. Please check your connection and try again.');
      } else if (error.message.includes('row-level security policy')) {
        throw new Error('Authentication failed. Please log out and back in.');
      } else {
        throw new Error(`Failed to update plant diagnosis: ${error.message}`);
      }
    }

    return data;
  }

  static async removePlantDiagnosis(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('plant_diagnoses')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error removing plant diagnosis:', error);
      return false;
    }

    return true;
  }

  // Helper function to sanitize array data
  private static sanitizeArray(arr: any[]): string[] {
    if (!Array.isArray(arr)) return [];
    return arr.filter(item =>
      item &&
      typeof item === 'string' &&
      item.trim().length > 0 &&
      item.trim() !== '.'
    ).map(item => item.trim());
  }

  // Helper function to sanitize string data
  private static sanitizeString(str: any): string | undefined {
    if (!str || typeof str !== 'string') return undefined;
    const trimmed = str.trim();
    return (trimmed.length > 0 && trimmed !== '.') ? trimmed : undefined;
  }

  // Create a diagnosis-only record with complete plant data stored directly
  static async createDiagnosisOnly(plant: Plant, imageUri: string, userId: string, diagnosisData: any, notes?: string, location?: string): Promise<PlantDiagnosis> {
    const diagnosis = await this.createPlantDiagnosis({
      user_id: userId,
      image_url: imageUri,
      problem_description: this.sanitizeString(diagnosisData.problemDescription),
      diagnosed_problem: this.sanitizeString(diagnosisData.diagnosedProblem),
      likely_causes: this.sanitizeArray(diagnosisData.likelyCauses),
      symptoms_observed: this.sanitizeString(diagnosisData.symptomsObserved),
      severity: diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical',
      immediate_actions: this.sanitizeArray(diagnosisData.immediateActions),
      long_term_care: this.sanitizeArray(diagnosisData.longTermCare),
      product_recommendations: this.sanitizeArray(diagnosisData.productRecommendations),
      step_by_step_instructions: this.sanitizeArray(diagnosisData.stepByStepInstructions),
      prevention_tips: this.sanitizeArray(diagnosisData.preventionTips),
      prognosis: this.sanitizeString(diagnosisData.prognosis),
      confidence_score: diagnosisData.confidence || 0.95,
      diagnosis_source: 'openrouter_api',
      is_verified: false,
      is_public: false, // Keep private for "No! Keep this to myself"
      notes: notes, // Store user's personal notes
      location: location, // Store location where photo was taken
      // Garden fields for new structure
      nickname: plant.commonName,
      health_status: mapSeverityToHealthStatus(diagnosisData.severity),
      allow_community_tips: false, // Private diagnoses don't allow community tips
      // Plant identification fields stored directly in plant_diagnoses
      scientific_name: plant.scientificName,
      common_name: plant.commonName,
      description: plant.description,
      care_instructions: plant.careInstructions,
      plant_type: plant.plantType,
      native_region: plant.nativeRegion,
      toxicity_level: plant.toxicityLevel,
      toxicity_warning: plant.toxicityWarning,
      growth_habit: plant.growthHabit,
      growth_rate: plant.growthRate,
      mature_height: plant.matureHeight,
      mature_width: plant.matureWidth,
      mature_description: plant.matureDescription,
      bloom_time: plant.bloomTime,
      flower_colors: plant.flowerColors,
      foliage_type: plant.foliageType,
      hardiness_zones: plant.hardinessZones,
      min_temperature: plant.minTemperature,
      pests_and_diseases: plant.pestsAndDiseases,
      fun_facts: plant.funFacts,
      uses: plant.uses,
      propagation: plant.propagation,
      seasonal_care: plant.seasonalCare,
      companion_plants: plant.companionPlants,
      maintenance_level: plant.maintenanceLevel,
      tags: plant.tags,
    });

    return diagnosis;
  }

  // Update diagnosis notes specifically
  static async updateDiagnosisNotes(diagnosisId: string, notes: string): Promise<PlantDiagnosis> {
    const { data, error } = await supabase
      .from('plant_diagnoses')
      .update({
        notes: notes // Store notes in the dedicated notes field
      })
      .eq('id', diagnosisId)
      .select()
      .single();

    if (error) {
      console.error('Error updating diagnosis notes:', error);
      if (error.message.includes('JWT') || error.message.includes('401')) {
        throw new Error('Session expired. Please log out and back in.');
      } else if (error.message.includes('network')) {
        throw new Error('Network error. Please check your connection and try again.');
      } else {
        throw new Error(`Failed to update diagnosis notes: ${error.message}`);
      }
    }

    return data;
  }

  // New methods for sharing functionality
  static async shareIdentificationOnly(plant: Plant, imageUri: string, userId: string): Promise<PlantIdentification> {
    const identification = await this.createPlantIdentification({
      user_id: userId,
      image_url: imageUri,
      scientific_name: plant.scientificName,
      common_name: plant.commonName,
      description: plant.description,
      care_instructions: JSON.stringify(plant.careInstructions),
      tags: plant.tags,
      confidence_score: 0.95, // Default confidence for shared items
      identification_source: 'openrouter_api',
      is_verified: false,
      is_public: true, // This is the key difference - making it public
      location_taken: undefined,
      // Add all the new fields from the Plant type
      plant_type: plant.plantType,
      native_region: plant.nativeRegion,
      toxicity_level: plant.toxicityLevel,
      toxicity_warning: plant.toxicityWarning,
      growth_habit: plant.growthHabit,
      growth_rate: plant.growthRate,
      mature_height: plant.matureHeight,
      mature_width: plant.matureWidth,
      mature_description: plant.matureDescription,
      bloom_time: plant.bloomTime,
      flower_colors: plant.flowerColors,
      foliage_type: plant.foliageType,
      hardiness_zones: plant.hardinessZones,
      min_temperature: plant.minTemperature,
      pests_and_diseases: plant.pestsAndDiseases,
      fun_facts: plant.funFacts,
      uses: plant.uses,
      propagation: plant.propagation,
      seasonal_care: plant.seasonalCare,
      companion_plants: plant.companionPlants,
      maintenance_level: plant.maintenanceLevel,
    });

    return identification;
  }

  static async shareDiagnosisOnly(plant: Plant, imageUri: string, userId: string, diagnosisData: any): Promise<PlantDiagnosis> {
    // First create the plant identification
    const plantIdentification = await this.createPlantIdentification({
      user_id: userId,
      image_url: imageUri,
      scientific_name: plant.scientificName,
      common_name: plant.commonName,
      description: plant.description,
      care_instructions: JSON.stringify(plant.careInstructions),
      tags: plant.tags,
      confidence_score: 0.95,
      identification_source: 'openrouter_api',
      is_verified: false,
      is_public: true,
      location_taken: undefined,
      // Add all the new fields from the Plant type
      plant_type: plant.plantType,
      native_region: plant.nativeRegion,
      toxicity_level: plant.toxicityLevel,
      toxicity_warning: plant.toxicityWarning,
      growth_habit: plant.growthHabit,
      growth_rate: plant.growthRate,
      mature_height: plant.matureHeight,
      mature_width: plant.matureWidth,
      mature_description: plant.matureDescription,
      bloom_time: plant.bloomTime,
      flower_colors: plant.flowerColors,
      foliage_type: plant.foliageType,
      hardiness_zones: plant.hardinessZones,
      min_temperature: plant.minTemperature,
      pests_and_diseases: plant.pestsAndDiseases,
      fun_facts: plant.funFacts,
      uses: plant.uses,
      propagation: plant.propagation,
      seasonal_care: plant.seasonalCare,
      companion_plants: plant.companionPlants,
      maintenance_level: plant.maintenanceLevel,
    });

    // Then create the diagnosis
    const diagnosis = await this.createPlantDiagnosis({
      user_id: userId,
      plant_identification_id: plantIdentification.id,
      image_url: imageUri,
      problem_description: this.sanitizeString(diagnosisData.problemDescription),
      diagnosed_problem: this.sanitizeString(diagnosisData.diagnosedProblem),
      likely_causes: this.sanitizeArray(diagnosisData.likelyCauses),
      symptoms_observed: this.sanitizeString(diagnosisData.symptomsObserved),
      severity: diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical',
      immediate_actions: this.sanitizeArray(diagnosisData.immediateActions),
      long_term_care: this.sanitizeArray(diagnosisData.longTermCare),
      product_recommendations: this.sanitizeArray(diagnosisData.productRecommendations),
      step_by_step_instructions: this.sanitizeArray(diagnosisData.stepByStepInstructions),
      prevention_tips: this.sanitizeArray(diagnosisData.preventionTips),
      prognosis: this.sanitizeString(diagnosisData.prognosis),
      confidence_score: diagnosisData.confidence,
      diagnosis_source: 'openrouter_api',
      is_verified: false,
      is_public: true, // This is the key difference - making it public
    });

    return diagnosis;
  }

  // addToGardenAndShare function REMOVED: garden_collections table dropped
  // This functionality is now handled through plant_identifications with is_public=true

  // Recovery Tracking methods - REMOVED: Tables dropped
  // Functions that previously wrote to recovery_tracking table have been removed
  // Recovery tracking functionality has been discontinued
}
