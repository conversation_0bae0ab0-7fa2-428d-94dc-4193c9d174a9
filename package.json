{"name": "App-PlantConnects-290725", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "bunx rork start -p ibt1eyb3sgyua43smxgjy --tunnel", "start-web": "bunx rork start -p ibt1eyb3sgyua43smxgjy --web --tunnel", "start-web-dev": "DEBUG=expo* bunx rork start -p ibt1eyb3sgyua43smxgjy --web --tunnel", "expo-start": "npx expo start", "expo-start-tunnel": "npx expo start --tunnel", "expo-start-lan": "npx expo start --lan", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web"}, "dependencies": {"@aws-sdk/client-s3": "^3.859.0", "@clerk/clerk-expo": "^2.14.20", "@expo/config-plugins": "~10.1.1", "@expo/vector-icons": "^14.1.0", "@nkzw/create-context-hook": "^1.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-query": "^5.83.0", "dotenv": "^17.2.1", "expo-auth-session": "^6.2.1", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-file-system": "^18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.539.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.3", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "expo": "^53.0.20", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}