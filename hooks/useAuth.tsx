import React, { createContext, useContext, useEffect } from 'react';
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-expo';
import { DatabaseService } from '@/services/database';
import { createAuthenticatedClient } from '@/lib/supabase';

interface AuthContextType {
  user: any | null;
  session: any | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<boolean>;
  isSessionValid: () => boolean;
  // Clerk specific methods
  isSignedIn: boolean;
  userId: string | null | undefined;
  getToken: (options?: { template?: string }) => Promise<string | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user } = useUser();
  const { signOut: clerkSignOut, isLoaded, isSignedIn, userId, getToken } = useClerkAuth();

  // Automatically create user profile when user signs in
  useEffect(() => {
    const createUserProfileIfNeeded = async () => {
      if (isLoaded && isSignedIn && userId && user) {
        try {
          // Get authenticated Supabase client
          const authenticatedClient = await createAuthenticatedClient(getToken);
          
          // Check if user profile already exists
          const { data: existingProfile, error: fetchError } = await authenticatedClient
            .from('user_profiles')
            .select('*')
            .eq('user_id', userId)
            .single();
          
          if (fetchError && fetchError.code !== 'PGRST116') {
            console.error('Error checking existing profile:', fetchError);
            return;
          }
          
          if (!existingProfile) {
            // Create new user profile using authenticated client
            const newProfile = {
              user_id: userId,
              username: user.username || user.emailAddresses?.[0]?.emailAddress?.split('@')[0] || `user_${userId.slice(-8)}`,
              display_name: user.fullName || user.firstName || user.username || 'Anonymous User',
              bio: '',
              avatar_url: user.imageUrl || '',
              location: '',
              website_url: '',
              is_public: true,
              allow_garden_sharing: true,
              allow_profile_indexing: true,
              experience_level: 'beginner' as const,
              total_identifications: 0,
              total_diagnoses: 0,
            };

            const { data, error } = await authenticatedClient
              .from('user_profiles')
              .insert(newProfile)
              .select()
              .single();

            if (error) {
              console.error('Error creating user profile:', error);
            } else {
              console.log('User profile created successfully for:', userId);
            }
          }
        } catch (error) {
          console.error('Error creating user profile:', error);
          // Don't throw error to avoid blocking the auth flow
          // User can still use the app, profile will be created when they visit profile screen
        }
      }
    };

    createUserProfileIfNeeded();
  }, [isLoaded, isSignedIn, userId, user, getToken]);

  const signOut = async () => {
    try {
      await clerkSignOut();
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const refreshSession = async (): Promise<boolean> => {
    try {
      // With Clerk, we can try to get a fresh token to verify session validity
      const token = await getToken();
      return !!token;
    } catch (error) {
      console.error('Error refreshing session:', error);
      return false;
    }
  };

  const isSessionValid = (): boolean => {
    // In Clerk, if the user is signed in and loaded, the session is valid
    return isLoaded && isSignedIn;
  };

  const value = {
    user,
    session: isSignedIn ? { user } : null, // Create a session-like object for compatibility
    loading: !isLoaded,
    signOut,
    refreshSession,
    isSessionValid,
    isSignedIn: isSignedIn || false,
    userId,
    getToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
